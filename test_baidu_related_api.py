#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试百度相关搜索API
"""
import requests
import json
import time
import random

def test_baidu_related_search_api(keyword):
    """测试百度相关搜索API"""
    url = "https://sp0.baidu.com/5a1Fazu8AA54nxGko9WTAnF6hhy/su"
    params = {
        'wd': keyword,
        'json': '1',
        'p': '3',
        'sid': '1',
        'req': '2',
        'csor': '1',
        'pwd': keyword,
        't': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://www.baidu.com/'
    }
    
    try:
        print(f"测试百度相关搜索API: {keyword}")
        response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
        response.raise_for_status()
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text[:200]}...")
        
        # 尝试解析JSON
        try:
            data = response.json()
            if isinstance(data, dict) and 's' in data:
                suggestions = data['s']
                print(f"  ✅ 找到 {len(suggestions)} 个相关搜索建议")
                for i, suggestion in enumerate(suggestions[:5]):
                    print(f"    {i+1}. {suggestion}")
                return suggestions
            else:
                print(f"  ❌ 响应格式异常: {data}")
                return []
        except json.JSONDecodeError as e:
            print(f"  ❌ JSON解析失败: {e}")
            return []
            
    except Exception as e:
        print(f"  ❌ 请求失败: {e}")
        return []

def test_baidu_zhidao_api(keyword):
    """测试百度知道建议API"""
    url = "https://zhidao.baidu.com/api/susug"
    params = {
        'query': keyword,
        'cp': 'utf-8',
        'cb': 'jQuery',
        't': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://zhidao.baidu.com/'
    }
    
    try:
        print(f"测试百度知道API: {keyword}")
        response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
        response.raise_for_status()
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text[:200]}...")
        
        # 处理JSONP响应
        content = response.text
        if 'jQuery(' in content:
            json_str = content.replace('jQuery(', '').rstrip(');')
            try:
                data = json.loads(json_str)
                if isinstance(data, dict) and 'data' in data:
                    suggestions = data['data']
                    if isinstance(suggestions, list):
                        results = [item.get('k', '') for item in suggestions if isinstance(item, dict) and 'k' in item]
                        results = [r for r in results if r]  # 过滤空字符串
                        print(f"  ✅ 找到 {len(results)} 个知道建议")
                        for i, suggestion in enumerate(results[:5]):
                            print(f"    {i+1}. {suggestion}")
                        return results
                else:
                    print(f"  ❌ 响应格式异常: {data}")
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")
        else:
            print(f"  ❌ 不是JSONP格式")
        
        return []
            
    except Exception as e:
        print(f"  ❌ 请求失败: {e}")
        return []

def test_alternative_baidu_apis():
    """测试其他百度API"""
    # 测试百度图片搜索建议
    def test_baidu_image_api(keyword):
        url = "https://suggestion.baidu.com/su"
        params = {
            'wd': keyword,
            'cb': 'window.baidu.sug',
            'p': '3',
            'sid': '1',
            'csor': '0',
            'from': 'image',
            't': str(int(time.time() * 1000))
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://image.baidu.com/'
        }
        
        try:
            print(f"测试百度图片搜索API: {keyword}")
            response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
            response.raise_for_status()
            
            content = response.text
            print(f"图片搜索响应: {content[:200]}...")
            
            if 'window.baidu.sug(' in content:
                json_str = content.replace('window.baidu.sug(', '').rstrip(');')
                try:
                    # 使用eval处理特殊字符
                    data = eval(json_str)
                    if isinstance(data, dict) and 's' in data and data['s']:
                        suggestions = data['s']
                        print(f"  ✅ 图片搜索找到 {len(suggestions)} 个建议")
                        return suggestions
                except:
                    pass
            
            return []
            
        except Exception as e:
            print(f"  ❌ 图片搜索API失败: {e}")
            return []
    
    return test_baidu_image_api

def main():
    """主函数"""
    test_keywords = ["种植牙", "烤瓷牙", "牙齿美白"]
    
    for keyword in test_keywords:
        print(f"\n{'='*60}")
        print(f"测试关键词: {keyword}")
        print(f"{'='*60}")
        
        all_suggestions = []
        
        # 测试百度相关搜索API
        related_suggestions = test_baidu_related_search_api(keyword)
        if related_suggestions:
            all_suggestions.extend(related_suggestions)
        
        time.sleep(2)
        
        # 测试百度知道API
        zhidao_suggestions = test_baidu_zhidao_api(keyword)
        if zhidao_suggestions:
            all_suggestions.extend(zhidao_suggestions)
        
        time.sleep(2)
        
        # 测试百度图片搜索API
        image_api_test = test_alternative_baidu_apis()
        image_suggestions = image_api_test(keyword)
        if image_suggestions:
            all_suggestions.extend(image_suggestions)
        
        # 统计结果
        unique_suggestions = list(set(all_suggestions))
        print(f"\n📊 {keyword} 总结:")
        print(f"  - 总建议数: {len(all_suggestions)}")
        print(f"  - 唯一建议数: {len(unique_suggestions)}")
        
        if unique_suggestions:
            print(f"  - 前10个唯一建议:")
            for i, suggestion in enumerate(unique_suggestions[:10], 1):
                print(f"    {i}. {suggestion}")
        
        time.sleep(3)  # 关键词间延迟
    
    print(f"\n{'='*60}")
    print("所有百度相关API测试完成！")

if __name__ == "__main__":
    main()
