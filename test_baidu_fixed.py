#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的百度相关搜索API
"""
import requests
import time

def test_baidu_related_fixed(keyword):
    """测试修复后的百度相关搜索API"""
    url = "https://suggestion.baidu.com/su"
    params = {
        'wd': keyword,
        'cb': 'window.baidu.sug',
        'p': '3',
        'sid': '1',
        'csor': '1',
        'from': 'pc_web',
        't': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.baidu.com/'
    }
    
    try:
        print(f"测试百度相关搜索API: {keyword}")
        response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
        response.raise_for_status()
        
        content = response.text
        print(f"响应内容: {content[:100]}...")
        
        if 'window.baidu.sug(' in content:
            js_obj_str = content.replace('window.baidu.sug(', '').rstrip(');')

            # 使用正则表达式提取s数组
            import re
            s_match = re.search(r's:\[(.*?)\]', js_obj_str)
            if s_match:
                s_content = s_match.group(1)
                # 提取引号内的内容
                suggestions = re.findall(r'"([^"]*)"', s_content)
                if suggestions:
                    print(f"  ✅ 找到 {len(suggestions)} 个建议")
                    for i, suggestion in enumerate(suggestions[:5]):
                        print(f"    {i+1}. {suggestion}")
                    return suggestions

            print(f"  ❌ 无法提取建议列表")
        else:
            print(f"  ❌ 不是预期的JSONP格式")
        
        return []
            
    except Exception as e:
        print(f"  ❌ 请求失败: {e}")
        return []

def main():
    """主函数"""
    test_keywords = ["种植牙", "烤瓷牙"]
    
    for keyword in test_keywords:
        print(f"\n{'='*50}")
        suggestions = test_baidu_related_fixed(keyword)
        print(f"关键词 '{keyword}' 获得 {len(suggestions)} 个建议")
        time.sleep(2)
    
    print(f"\n{'='*50}")
    print("测试完成！")

if __name__ == "__main__":
    main()
