#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多个搜索引擎的建议API
"""
import requests
import json
import time
import random

def test_baidu_mobile_api(keyword):
    """测试百度移动端建议API"""
    url = "https://m.baidu.com/su"
    params = {
        'wd': keyword,
        'action': 'opensearch',
        'ie': 'utf-8',
        'from': 'result',
        't': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://m.baidu.com/'
    }
    
    try:
        print(f"测试百度移动端API: {keyword}")
        response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
        response.raise_for_status()
        
        data = response.json()
        if isinstance(data, list) and len(data) > 1 and isinstance(data[1], list):
            suggestions = data[1]
            print(f"  ✅ 找到 {len(suggestions)} 个建议")
            return suggestions
        else:
            print(f"  ❌ 响应格式异常")
            return []
            
    except Exception as e:
        print(f"  ❌ 请求失败: {e}")
        return []

def test_baidu_pc_api(keyword):
    """测试百度PC端建议API"""
    url = "https://suggestion.baidu.com/su"
    params = {
        'wd': keyword,
        'cb': 'window.baidu.sug',
        'p': '3',
        'sid': '1',
        'csor': '0',
        't': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.baidu.com/'
    }
    
    try:
        print(f"测试百度PC端API: {keyword}")
        response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
        response.raise_for_status()
        
        content = response.text
        if 'window.baidu.sug(' in content:
            json_str = content.replace('window.baidu.sug(', '').rstrip(');')
            data = json.loads(json_str)
            if 's' in data and data['s']:
                suggestions = data['s']
                print(f"  ✅ 找到 {len(suggestions)} 个建议")
                return suggestions
        
        print(f"  ❌ 响应格式异常")
        return []
            
    except Exception as e:
        print(f"  ❌ 请求失败: {e}")
        return []

def test_360_api(keyword):
    """测试360搜索建议API"""
    url = "https://sug.so.360.cn/suggest"
    params = {
        'word': keyword,
        'encodein': 'utf-8',
        'encodeout': 'utf-8',
        'format': 'json',
        't': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.so.com/'
    }
    
    try:
        print(f"测试360搜索API: {keyword}")
        response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
        response.raise_for_status()
        
        data = response.json()
        if isinstance(data, dict) and 'result' in data:
            result = data['result']
            if isinstance(result, list):
                suggestions = [item.get('word', '') for item in result if isinstance(item, dict) and 'word' in item]
                suggestions = [s for s in suggestions if s]  # 过滤空字符串
                print(f"  ✅ 找到 {len(suggestions)} 个建议")
                return suggestions
        
        print(f"  ❌ 响应格式异常")
        return []
            
    except Exception as e:
        print(f"  ❌ 请求失败: {e}")
        return []

def test_sogou_api(keyword):
    """测试搜狗搜索建议API"""
    url = "https://suggestion.sogou.com/suggestion"
    params = {
        'key': keyword,
        'type': 'web',
        'ori': 'index',
        't': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.sogou.com/'
    }
    
    try:
        print(f"测试搜狗搜索API: {keyword}")
        response = requests.get(url, params=params, headers=headers, timeout=10, verify=False)
        response.raise_for_status()
        
        content = response.text
        # 搜狗可能返回JSONP格式
        if content.startswith('window.sogou.sug('):
            json_str = content.replace('window.sogou.sug(', '').rstrip(');')
            data = json.loads(json_str)
            if isinstance(data, list) and len(data) > 1:
                suggestions = data[1]
                print(f"  ✅ 找到 {len(suggestions)} 个建议")
                return suggestions
        else:
            # 尝试直接解析JSON
            try:
                data = json.loads(content)
                # 根据实际响应格式调整解析逻辑
                print(f"  ⚠️ 需要调整解析逻辑: {content[:100]}...")
            except:
                print(f"  ❌ 响应格式异常: {content[:100]}...")
        
        return []
            
    except Exception as e:
        print(f"  ❌ 请求失败: {e}")
        return []

def test_all_apis(keyword):
    """测试所有API"""
    print(f"\n{'='*50}")
    print(f"测试关键词: {keyword}")
    print(f"{'='*50}")
    
    all_suggestions = []
    
    # 测试各个API
    apis = [
        ("百度移动端", test_baidu_mobile_api),
        ("百度PC端", test_baidu_pc_api),
        ("360搜索", test_360_api),
        ("搜狗搜索", test_sogou_api)
    ]
    
    for api_name, api_func in apis:
        try:
            suggestions = api_func(keyword)
            if suggestions:
                all_suggestions.extend(suggestions)
                print(f"  📝 {api_name}前3个建议: {suggestions[:3]}")
            time.sleep(1)  # 延迟避免请求过快
        except Exception as e:
            print(f"  ❌ {api_name}测试异常: {e}")
    
    # 去重统计
    unique_suggestions = list(set(all_suggestions))
    print(f"\n📊 总结:")
    print(f"  - 总建议数: {len(all_suggestions)}")
    print(f"  - 唯一建议数: {len(unique_suggestions)}")
    
    if unique_suggestions:
        print(f"  - 前10个唯一建议:")
        for i, suggestion in enumerate(unique_suggestions[:10], 1):
            print(f"    {i}. {suggestion}")
    
    return unique_suggestions

def main():
    """主函数"""
    test_keywords = ["种植牙", "烤瓷牙", "牙齿美白"]
    
    for keyword in test_keywords:
        test_all_apis(keyword)
        time.sleep(2)  # 关键词间延迟
    
    print(f"\n{'='*50}")
    print("所有API测试完成！")

if __name__ == "__main__":
    main()
