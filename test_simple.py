#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试多API功能
"""
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

# 导入主程序的类
try:
    exec(open('bd_wap - 采集助手.py', encoding='utf-8').read(), globals())
    
    # 创建爬虫实例
    from queue import Queue
    import threading
    
    task_queue = Queue()
    failed_log = open('test_failed.log', 'w', encoding='utf-8')
    
    spider = BaiduMobileSpider(task_queue, 2, failed_log)
    
    # 测试单个关键词
    keyword = "烤瓷牙"
    print(f"测试关键词: {keyword}")
    
    suggestions = spider.download_page(keyword)
    print(f"获得建议: {len(suggestions) if suggestions else 0} 个")
    
    if suggestions:
        print("前5个建议:")
        for i, suggestion in enumerate(suggestions[:5]):
            print(f"  {i+1}. {suggestion}")
    
    failed_log.close()
    print("测试完成！")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
