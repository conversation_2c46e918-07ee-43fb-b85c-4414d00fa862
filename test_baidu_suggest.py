#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试百度搜索建议API
"""
import requests
import json
import time
import random

def test_baidu_suggest_api(keyword):
    """测试百度搜索建议API"""
    # 百度搜索建议API
    url = "https://suggestion.baidu.com/su"
    
    params = {
        'wd': keyword,
        'cb': 'window.baidu.sug',
        'p': '3',
        'sid': '1',
        'csor': '0',
        't': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://www.baidu.com/'
    }
    
    try:
        print(f"正在测试百度建议API: {keyword}")
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        
        # 解析JSONP响应
        content = response.text
        print(f"原始响应: {content[:200]}...")
        
        # 提取JSON部分
        if 'window.baidu.sug(' in content:
            json_str = content.replace('window.baidu.sug(', '').rstrip(');')
            data = json.loads(json_str)
            
            if 's' in data and data['s']:
                suggestions = data['s']
                print(f"找到 {len(suggestions)} 个建议:")
                for i, suggestion in enumerate(suggestions):
                    print(f"  {i+1}. {suggestion}")
                return suggestions
            else:
                print("没有找到建议")
                return []
        else:
            print("响应格式不正确")
            return []
            
    except Exception as e:
        print(f"API请求失败: {e}")
        return []

def test_baidu_mobile_suggest(keyword):
    """测试百度移动端建议API"""
    url = "https://m.baidu.com/su"
    
    params = {
        'wd': keyword,
        'action': 'opensearch',
        'ie': 'utf-8',
        'from': 'result',
        't': str(int(time.time() * 1000))
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://m.baidu.com/'
    }
    
    try:
        print(f"正在测试百度移动端建议API: {keyword}")
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        
        print(f"移动端响应: {response.text[:200]}...")
        
        # 尝试解析JSON
        try:
            data = response.json()
            if isinstance(data, list) and len(data) > 1:
                suggestions = data[1]  # 通常建议在第二个元素
                print(f"找到 {len(suggestions)} 个移动端建议:")
                for i, suggestion in enumerate(suggestions):
                    print(f"  {i+1}. {suggestion}")
                return suggestions
        except:
            pass
            
        return []
        
    except Exception as e:
        print(f"移动端API请求失败: {e}")
        return []

def test_alternative_search_methods(keyword):
    """测试其他搜索方法"""
    print(f"\n=== 测试关键词: {keyword} ===")
    
    # 测试PC端建议API
    print("\n1. 测试PC端建议API:")
    pc_suggestions = test_baidu_suggest_api(keyword)
    
    time.sleep(2)
    
    # 测试移动端建议API
    print("\n2. 测试移动端建议API:")
    mobile_suggestions = test_baidu_mobile_suggest(keyword)
    
    # 合并结果
    all_suggestions = list(set(pc_suggestions + mobile_suggestions))
    
    print(f"\n总共获得 {len(all_suggestions)} 个唯一建议:")
    for i, suggestion in enumerate(all_suggestions):
        print(f"  {i+1}. {suggestion}")
    
    return all_suggestions

def main():
    """主函数"""
    test_keywords = ["烤瓷牙", "种植牙", "牙齿美白"]
    
    all_results = {}
    
    for keyword in test_keywords:
        suggestions = test_alternative_search_methods(keyword)
        all_results[keyword] = suggestions
        
        # 延迟避免请求过快
        time.sleep(random.uniform(3, 5))
    
    print(f"\n{'='*50}")
    print("所有结果汇总:")
    for keyword, suggestions in all_results.items():
        print(f"\n{keyword}: {len(suggestions)} 个建议")
        for suggestion in suggestions[:10]:  # 只显示前10个
            print(f"  - {suggestion}")

if __name__ == "__main__":
    main()
