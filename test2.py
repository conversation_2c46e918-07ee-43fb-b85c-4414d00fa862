import requests,json

def get_mobile_suggestions_via_thirdparty(keyword):
    """
    通过第三方API获取移动端搜索建议词
    :param keyword: 要查询的关键词
    :return: 相关搜索词列表
    """
    url = "https://api.mobile.baidu.com/json"
    params = {
        'type': 'sug',
        'q': keyword,
        'cb': 'jQuery123',
        '_': '1678888888888'  # 时间戳
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        'Origin': 'https://m.baidu.com',
        'Referer': 'https://m.baidu.com/'
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=5)
        if response.status_code == 200:
            # 处理JSONP响应
            json_str = response.text[len('jQuery123('):-1]
            data = json.loads(json_str)
            return data.get('s', [])
        return []
    except Exception as e:
        print(f"通过第三方API获取移动端建议词出错: {e}")
        return []
if __name__ == '__main__':
    print(get_mobile_suggestions_via_thirdparty('种植牙'))