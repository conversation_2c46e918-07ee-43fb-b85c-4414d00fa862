#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理池测试脚本
"""
import requests
import time

def test_proxy():
    """测试代理功能"""
    # 代理服务器信息
    proxy_host = "**************"
    proxy_port = 40027
    
    # 目标URL
    target_url = "http://myip.ipip.net"
    
    # 构建代理配置
    proxy_meta = f"http://{proxy_host}:{proxy_port}"
    proxies = {
        "http": proxy_meta,
        "https": proxy_meta
    }
    
    # 请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
    }
    
    try:
        print(f"正在测试代理: {proxy_host}:{proxy_port}")
        start_time = int(round(time.time() * 1000))
        
        # 发送请求
        response = requests.get(
            target_url, 
            proxies=proxies, 
            headers=headers,
            timeout=10,
            verify=False
        )
        
        cost_time = int(round(time.time() * 1000)) - start_time
        
        print(f"响应内容: {response.text.strip()}")
        print(f"耗时: {cost_time}ms")
        print("代理测试成功！")
        
    except Exception as e:
        print(f"代理测试失败: {e}")

def test_proxy_pool():
    """测试代理池获取功能"""
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))

    # 导入代理池类
    exec(open('bd_wap - 采集助手.py', encoding='utf-8').read(), globals())
    proxy_pool = ProxyPool()
    
    print("正在测试代理池...")
    
    # 获取代理列表
    if proxy_pool.fetch_proxies():
        print(f"成功获取 {len(proxy_pool.proxies)} 个代理")
        
        # 测试前5个代理
        for i, proxy in enumerate(proxy_pool.proxies[:5]):
            print(f"\n测试代理 {i+1}: {proxy}")
            
            proxy_dict = proxy_pool.get_proxy_dict(proxy)
            target_url = "http://myip.ipip.net"
            
            try:
                response = requests.get(
                    target_url,
                    proxies=proxy_dict,
                    timeout=10,
                    verify=False
                )
                print(f"  成功: {response.text.strip()}")
            except Exception as e:
                print(f"  失败: {e}")
                proxy_pool.mark_failed(proxy)
    else:
        print("获取代理失败")

if __name__ == "__main__":
    print("=== 代理功能测试 ===")
    
    # 测试单个代理
    print("\n1. 测试单个代理:")
    test_proxy()
    
    # 测试代理池
    print("\n2. 测试代理池:")
    test_proxy_pool()
