import requests
import json

def get_baidu_related_searches(keyword, count=10):
    """
    获取百度相关搜索词 (模拟百度智能推荐引擎)
    :param keyword: 要查询的关键词
    :param count: 返回的相关词数量
    :return: 相关搜索词列表
    """
    url = "https://www.baidu.com/s"
    params = {
        'wd': keyword,
        'rn': count,  # 返回结果数量
        'ie': 'utf-8'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=5)

        if response.status_code == 200:
            # 从HTML中解析相关搜索词
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            #中间相关搜索， 找class 是pc-rg-upgrade_开头的div
            # related_div_middle = soup.select('div[class^="pc-rg-upgrade_"]')[0]
            # if related_div_middle:
            #     #获取card-show-log属性的值
            #     card_show_log_middle = related_div_middle['card-show-log']
            #     card_show_log_middle = json.loads(card_show_log_middle)
            #     related_words_middle = card_show_log_middle['iteminfo']
            #     middle_related_words = [item['item'] for item in related_words_middle]
            #     print("中间相关搜索:",middle_related_words)

            

            #右侧相关搜索
            related_div_right = soup.find('div', {'card-type': 'rg'})
            if related_div_right:
                #获取card-show-log属性的值
                card_show_log = related_div_right['card-show-log']
                card_show_log = json.loads(card_show_log)
                related_words = card_show_log['iteminfo']
                right_related_words = [item['item'] for item in related_words]
                print("右侧相关搜索:",right_related_words)
            
            # 查找相关搜索区域
            related_div = soup.find('div', {'id': 'rs_new'})
            if related_div:
                related_links = related_div.find_all('a')
                return [link.text.strip() for link in related_links]
        return []
    except Exception as e:
        print(f"获取百度相关搜索出错: {e}")
        return []

# 使用示例
if __name__ == "__main__":
    keyword = "机器学习"
    related_searches = get_baidu_related_searches(keyword, 8)
    print(f"与 '{keyword}' 相关的底部搜索词:")
    for i, word in enumerate(related_searches, 1):
        print(f"{i}. {word}")