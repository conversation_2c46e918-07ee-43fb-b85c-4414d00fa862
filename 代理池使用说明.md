# 百度移动端关键词挖掘工具 - 代理池功能说明

## 功能概述

本工具已集成代理池功能，可以自动获取和管理代理IP，提高采集成功率和避免IP被封。

## 代理池特性

### 1. 自动获取代理
- 从神龙IP服务商自动获取400个代理IP
- 支持HTTP/HTTPS协议
- 自动解析代理列表格式

### 2. 智能代理管理
- 轮询使用代理IP，避免单个代理过载
- 自动检测失效代理并标记
- 失效代理自动重新获取
- 线程安全的代理池管理

### 3. 容错机制
- 单个请求最多重试3次
- 代理失效时自动切换到下一个代理
- 所有代理失效时自动重新获取代理列表
- 支持无代理模式降级

## 使用方法

### GUI界面使用

1. **启动程序**
   ```bash
   python "bd_wap - 采集助手.py"
   ```

2. **配置代理**
   - 勾选"使用代理池"选项
   - 点击"获取代理"按钮手动获取代理列表
   - 查看代理状态显示

3. **开始挖掘**
   - 配置其他参数（线程数、深度等）
   - 点击"开始挖掘"
   - 程序会自动初始化代理池并开始采集

### 命令行使用

```bash
python "bd_wap - 采集助手.py" --cli
```

命令行模式默认启用代理池功能。

## 代理配置

### 代理服务商信息
- **服务商**: 神龙IP
- **API地址**: http://api.shenlongip.com/ip
- **获取数量**: 400个代理IP
- **协议**: HTTP

### 代理格式
代理IP格式为：`IP:端口`，例如：
```
**************:40027
**************:40031
***********:40006
```

## 技术实现

### ProxyPool类
```python
class ProxyPool:
    def fetch_proxies(self)      # 获取代理列表
    def get_proxy(self)          # 获取可用代理
    def mark_failed(self, proxy) # 标记失效代理
    def get_proxy_dict(self, proxy) # 转换代理格式
```

### 请求流程
1. 获取代理IP
2. 构建代理配置
3. 发送HTTP请求
4. 处理响应或错误
5. 失败时标记代理并重试

## 注意事项

### 1. 网络要求
- 需要稳定的网络连接
- 确保能访问代理服务商API
- 代理IP质量依赖服务商

### 2. 性能考虑
- 代理请求会增加延迟
- 建议适当调整线程数（推荐8-16个）
- 失效代理会自动重试，影响速度

### 3. 错误处理
- 代理获取失败时会提示用户选择
- 可以选择继续使用无代理模式
- 所有网络错误都有详细日志

## 测试代理功能

使用提供的测试脚本：
```bash
python test_proxy.py
```

测试内容包括：
- 单个代理连接测试
- 代理池获取功能测试
- 前5个代理的可用性测试

## 故障排除

### 1. 代理获取失败
- 检查网络连接
- 确认API密钥有效
- 查看错误日志信息

### 2. 代理连接失败
- 代理IP可能已失效
- 目标网站可能限制代理访问
- 尝试重新获取代理列表

### 3. 采集速度慢
- 减少并发线程数
- 检查代理质量
- 考虑使用更高质量的代理服务

## 更新日志

- **v2.1** (2025-07-30)
  - 新增代理池功能
  - 支持自动获取神龙IP代理
  - 添加代理失效检测和重试机制
  - 优化GUI界面，增加代理状态显示
  - 支持代理开关控制
