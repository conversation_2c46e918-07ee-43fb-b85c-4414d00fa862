#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML调试工具 - 用于分析百度移动端页面结构
"""
import re
import requests
import time
import random

def get_baidu_mobile_page(keyword):
    """获取百度移动端搜索页面（增强反检测）"""
    # 构建搜索URL，添加更多参数模拟真实用户
    base_url = "https://m.baidu.com/s"
    params = {
        'word': keyword,
        'sa': 'tb',
        'ts': str(int(time.time() * 1000)),  # 时间戳
        'rsv_pq': f"{random.randint(100000000000000, 999999999999999)}",  # 随机参数
        'rsv_t': f"{random.randint(1000, 9999)}",
        'rqlang': 'cn'
    }

    # 随机选择User-Agent
    user_agents = [
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Mobile Safari/537.36'
    ]

    headers = {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Referer': 'https://www.baidu.com/'
    }

    try:
        print(f"正在获取页面: {base_url}?word={keyword}")

        # 创建会话
        session = requests.Session()

        # 添加随机延迟
        time.sleep(random.uniform(2, 4))

        response = session.get(
            base_url,
            params=params,
            headers=headers,
            timeout=20,
            verify=False,
            allow_redirects=True
        )
        response.raise_for_status()

        # 检查是否是安全验证页面
        if "安全验证" in response.text or "网络不给力" in response.text or "mkdjump" in response.text:
            print("⚠️ 遇到安全验证页面")
            return response.text  # 仍然返回，用于分析

        print("✅ 成功获取搜索结果页面")
        return response.text

    except Exception as e:
        print(f"获取页面失败: {e}")
        return None

def analyze_html_structure(html, keyword):
    """分析HTML结构，寻找可能的关键词位置"""
    if not html:
        return
    
    print(f"\n=== 分析关键词: {keyword} ===")
    print(f"HTML长度: {len(html)} 字符")
    
    # 保存完整HTML到文件
    filename = f"debug_{keyword}_full.html"
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html)
        print(f"完整HTML已保存到: {filename}")
    except Exception as e:
        print(f"保存HTML失败: {e}")
    
    # 查找可能包含关键词的区域
    patterns_to_check = [
        (r'<a[^>]*?href="[^"]*?word=[^"]*?"[^>]*?>(.*?)</a>', "搜索链接"),
        (r'<span[^>]*?class="[^"]*?"[^>]*?>(.*?)</span>', "Span标签"),
        (r'<div[^>]*?class="[^"]*?rela[^"]*?"[^>]*?>(.*?)</div>', "相关搜索区域"),
        (r'data-query="([^"]+)"', "数据查询属性"),
        (r'<em[^>]*?>(.*?)</em>', "强调标签"),
        (r'title="([^"]*?)"', "标题属性"),
        (r'<h[1-6][^>]*?>(.*?)</h[1-6]>', "标题标签"),
    ]
    
    for pattern_str, description in patterns_to_check:
        try:
            pattern = re.compile(pattern_str, re.S|re.I)
            matches = pattern.findall(html)
            
            if matches:
                print(f"\n{description} 找到 {len(matches)} 个匹配:")
                for i, match in enumerate(matches[:10]):  # 只显示前10个
                    cleaned = clean_debug_text(match)
                    if cleaned:
                        print(f"  {i+1}. {cleaned}")
            else:
                print(f"\n{description}: 无匹配")
                
        except Exception as e:
            print(f"正则匹配错误 {description}: {e}")
    
    # 查找包含中文的文本片段
    print(f"\n=== 中文文本片段分析 ===")
    chinese_pattern = re.compile(r'[\u4e00-\u9fa5]{2,}', re.S)
    chinese_matches = chinese_pattern.findall(html)
    
    if chinese_matches:
        print(f"找到 {len(chinese_matches)} 个中文片段:")
        unique_chinese = list(set(chinese_matches))[:20]  # 去重并限制数量
        for i, text in enumerate(unique_chinese):
            if len(text) >= 2 and len(text) <= 20:
                print(f"  {i+1}. {text}")

def clean_debug_text(text):
    """清理调试文本"""
    if not text:
        return ""
    
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    
    # 移除特殊字符
    text = re.sub(r'[\r\n\t]+', ' ', text)
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    
    # 过滤太短或太长的文本
    if len(text) < 2 or len(text) > 50:
        return ""
    
    # 过滤纯数字或符号
    if re.match(r'^[\d\s\-_=+<>()（）【】\[\]]+$', text):
        return ""
    
    return text

def main():
    """主函数"""
    test_keywords = ["烤瓷牙", "种植牙", "牙齿美白"]
    
    for keyword in test_keywords:
        print(f"\n{'='*50}")
        html = get_baidu_mobile_page(keyword)
        if html:
            analyze_html_structure(html, keyword)
        
        # 延迟避免请求过快
        time.sleep(random.uniform(2, 4))
    
    print(f"\n{'='*50}")
    print("分析完成！请查看生成的HTML文件了解页面结构。")

if __name__ == "__main__":
    main()
